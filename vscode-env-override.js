// VSCode环境变量覆盖脚本
// 用于在VSCode启动时注入固定的机器ID

const fs = require('fs');
const path = require('path');
const os = require('os');

// 固定的机器ID
const FIXED_MACHINE_ID = "090c9851dc1f842312c0ad663ac03a01f9eaf6be01f5c792dfb22bfae679208c";

// VSCode配置目录
const vscodeConfigDir = path.join(os.homedir(), '.vscode');
const userDataDir = path.join(vscodeConfigDir, 'User');

function createEnvironmentOverride() {
    try {
        // 确保目录存在
        if (!fs.existsSync(userDataDir)) {
            fs.mkdirSync(userDataDir, { recursive: true });
        }
        
        // 创建启动脚本
        const startupScript = `
// VSCode环境覆盖脚本
// 在扩展加载前注入固定机器ID

(function() {
    // 覆盖process.env中的相关变量
    if (typeof process !== 'undefined' && process.env) {
        process.env.VSCODE_MACHINE_ID = "${FIXED_MACHINE_ID}";
        process.env.VSCODE_SESSION_ID = "${FIXED_MACHINE_ID}";
    }
    
    // 覆盖VSCode API
    if (typeof vscode !== 'undefined' && vscode.env) {
        Object.defineProperty(vscode.env, 'machineId', {
            value: "${FIXED_MACHINE_ID}",
            writable: false,
            configurable: false
        });
        
        Object.defineProperty(vscode.env, 'sessionId', {
            value: "${FIXED_MACHINE_ID}",
            writable: false,
            configurable: false
        });
    }
    
    console.log('🔧 机器ID已固定为: ${FIXED_MACHINE_ID}');
})();
`;
        
        // 写入启动脚本
        const scriptPath = path.join(userDataDir, 'machine-id-override.js');
        fs.writeFileSync(scriptPath, startupScript);
        
        // 创建VSCode设置文件来加载脚本
        const settingsPath = path.join(userDataDir, 'settings.json');
        let settings = {};
        
        if (fs.existsSync(settingsPath)) {
            settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
        }
        
        // 添加启动时执行的脚本
        settings['workbench.startupEditor'] = 'none';
        
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
        
        console.log('✅ 环境覆盖脚本已创建');
        console.log(`📁 脚本位置: ${scriptPath}`);
        console.log(`📝 固定机器ID: ${FIXED_MACHINE_ID}`);
        
    } catch (error) {
        console.error('❌ 创建环境覆盖失败:', error.message);
    }
}

createEnvironmentOverride();