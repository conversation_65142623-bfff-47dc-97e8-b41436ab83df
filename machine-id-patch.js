// 机器ID固定补丁
// 这个脚本用于修改Augment扩展，使其返回固定的机器ID

const fs = require('fs');
const path = require('path');

// 目标机器ID - 你可以修改这个值
const FIXED_MACHINE_ID = "090c9851dc1f842312c0ad663ac03a01f9eaf6be01f5c792dfb22bfae679208c";

// 扩展文件路径
const extensionPath = './extension/out/extension.js';

function patchExtension() {
    try {
        // 读取原始文件
        let content = fs.readFileSync(extensionPath, 'utf8');
        
        // 方法1: 替换UUID生成函数
        // 查找并替换randomUUID函数调用
        content = content.replace(
            /Gre\.randomUUID\(\)/g,
            `"${FIXED_MACHINE_ID}"`
        );
        
        // 方法2: 替换crypto.randomUUID调用
        content = content.replace(
            /require\("crypto"\)\.randomUUID\(\)/g,
            `"${FIXED_MACHINE_ID}"`
        );
        
        // 方法3: 替换uuid.v4()调用
        content = content.replace(
            /uuid\.v4\(\)/g,
            `"${FIXED_MACHINE_ID}"`
        );
        
        // 方法4: 在文件开头注入固定ID覆盖
        const injectionCode = `
// 机器ID固定补丁 - 注入代码
(function() {
    const originalRandomUUID = require('crypto').randomUUID;
    require('crypto').randomUUID = function() {
        return "${FIXED_MACHINE_ID}";
    };
    
    // 如果存在全局的uuid模块，也要覆盖
    if (typeof global !== 'undefined' && global.uuid) {
        const originalV4 = global.uuid.v4;
        global.uuid.v4 = function() {
            return "${FIXED_MACHINE_ID}";
        };
    }
})();
`;
        
        // 在文件开头注入代码
        content = injectionCode + content;
        
        // 写回文件
        fs.writeFileSync(extensionPath + '.patched', content);
        
        console.log('✅ 扩展已成功打补丁');
        console.log(`📝 固定机器ID: ${FIXED_MACHINE_ID}`);
        console.log(`📁 补丁文件: ${extensionPath}.patched`);
        console.log('');
        console.log('使用说明:');
        console.log('1. 备份原始文件: cp extension/out/extension.js extension/out/extension.js.backup');
        console.log('2. 替换文件: cp extension/out/extension.js.patched extension/out/extension.js');
        console.log('3. 重新加载VSCode扩展');
        
    } catch (error) {
        console.error('❌ 打补丁失败:', error.message);
    }
}

// 执行补丁
patchExtension();