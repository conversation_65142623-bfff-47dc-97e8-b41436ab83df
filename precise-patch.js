// 精确的Augment扩展机器ID修改脚本
// 这个脚本会精确定位并修改机器ID生成的关键位置

const fs = require('fs');
const path = require('path');

// 目标机器ID
const FIXED_MACHINE_ID = "090c9851dc1f842312c0ad663ac03a01f9eaf6be01f5c792dfb22bfae679208c";

// 扩展文件路径
const extensionPath = './extension/out/extension.js';

function applyPrecisePatch() {
    try {
        console.log('🔍 正在分析扩展代码...');
        
        // 读取原始文件
        let content = fs.readFileSync(extensionPath, 'utf8');
        
        // 备份原始文件
        fs.writeFileSync(extensionPath + '.original', content);
        console.log('💾 已创建原始文件备份');
        
        let patchCount = 0;
        
        // 1. 替换crypto.randomUUID()调用
        const cryptoUUIDPattern = /require\("crypto"\)\.randomUUID\(\)/g;
        if (content.match(cryptoUUIDPattern)) {
            content = content.replace(cryptoUUIDPattern, `"${FIXED_MACHINE_ID}"`);
            patchCount++;
            console.log('✅ 已修补crypto.randomUUID()调用');
        }
        
        // 2. 替换Gre.randomUUID()调用（从代码分析中发现的）
        const greUUIDPattern = /Gre\.randomUUID\(\)/g;
        if (content.match(greUUIDPattern)) {
            content = content.replace(greUUIDPattern, `"${FIXED_MACHINE_ID}"`);
            patchCount++;
            console.log('✅ 已修补Gre.randomUUID()调用');
        }
        
        // 3. 替换uuid.v4()调用
        const uuidV4Pattern = /uuid\.v4\(\)/g;
        if (content.match(uuidV4Pattern)) {
            content = content.replace(uuidV4Pattern, `"${FIXED_MACHINE_ID}"`);
            patchCount++;
            console.log('✅ 已修补uuid.v4()调用');
        }
        
        // 4. 替换sessionId生成
        const sessionIdPattern = /sessionId\s*=\s*[^;,}]+/g;
        const sessionIdMatches = content.match(sessionIdPattern);
        if (sessionIdMatches) {
            content = content.replace(sessionIdPattern, `sessionId="${FIXED_MACHINE_ID}"`);
            patchCount++;
            console.log('✅ 已修补sessionId生成');
        }
        
        // 5. 在文件开头注入全局覆盖代码
        const globalOverride = `
// === 机器ID固定补丁 - 全局覆盖 ===
(function() {
    const FIXED_MACHINE_ID = "${FIXED_MACHINE_ID}";
    
    // 覆盖crypto模块
    const originalRequire = require;
    require = function(moduleName) {
        const module = originalRequire.apply(this, arguments);
        if (moduleName === 'crypto' && module.randomUUID) {
            const originalRandomUUID = module.randomUUID;
            module.randomUUID = function() {
                console.log('🔧 使用固定机器ID:', FIXED_MACHINE_ID);
                return FIXED_MACHINE_ID;
            };
        }
        return module;
    };
    
    // 覆盖全局对象
    if (typeof global !== 'undefined') {
        global.AUGMENT_FIXED_MACHINE_ID = FIXED_MACHINE_ID;
    }
    
    console.log('🚀 Augment扩展机器ID已固定为:', FIXED_MACHINE_ID);
})();

`;
        
        content = globalOverride + content;
        patchCount++;
        
        // 6. 特殊处理：查找并替换特定的机器ID生成模式
        // 基于之前分析的代码结构
        const machineIdGenerationPattern = /([a-f0-9]{64})/g;
        const existingIds = content.match(machineIdGenerationPattern);
        if (existingIds) {
            console.log(`🔍 发现 ${existingIds.length} 个可能的机器ID`);
            // 只替换看起来像机器ID的长字符串
            content = content.replace(/[a-f0-9]{64}/g, FIXED_MACHINE_ID);
            patchCount++;
            console.log('✅ 已替换现有机器ID');
        }
        
        // 写入修改后的文件
        fs.writeFileSync(extensionPath, content);
        
        console.log('');
        console.log('🎉 补丁应用完成!');
        console.log(`📊 总共应用了 ${patchCount} 个补丁`);
        console.log(`🆔 固定机器ID: ${FIXED_MACHINE_ID}`);
        console.log('');
        console.log('📋 后续步骤:');
        console.log('1. 重启VSCode');
        console.log('2. 重新加载Augment扩展');
        console.log('3. 检查扩展是否正常工作');
        console.log('');
        console.log('🔄 如需恢复原始版本:');
        console.log(`   cp "${extensionPath}.original" "${extensionPath}"`);
        
    } catch (error) {
        console.error('❌ 应用补丁失败:', error.message);
        console.error('详细错误:', error);
    }
}

// 执行补丁
applyPrecisePatch();