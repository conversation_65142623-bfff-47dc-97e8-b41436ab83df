@echo off
REM Windows系统级机器ID固定脚本
REM 需要管理员权限运行

echo 正在固定VSCode机器ID...

REM 固定的机器ID
set FIXED_ID=090c9851dc1f842312c0ad663ac03a01f9eaf6be01f5c792dfb22bfae679208c

REM 设置环境变量
setx VSCODE_MACHINE_ID "%FIXED_ID%" /M
setx VSCODE_SESSION_ID "%FIXED_ID%" /M

REM 修改VSCode配置目录
set VSCODE_DIR=%APPDATA%\Code\User

REM 创建机器ID文件
echo %FIXED_ID% > "%VSCODE_DIR%\machineId"

REM 修改VSCode的存储文件
if exist "%APPDATA%\Code\User\globalStorage\state.vscdb" (
    echo 正在备份VSCode状态文件...
    copy "%APPDATA%\Code\User\globalStorage\state.vscdb" "%APPDATA%\Code\User\globalStorage\state.vscdb.backup"
)

REM 创建启动脚本
echo @echo off > "%TEMP%\vscode-fixed-id.bat"
echo set VSCODE_MACHINE_ID=%FIXED_ID% >> "%TEMP%\vscode-fixed-id.bat"
echo set VSCODE_SESSION_ID=%FIXED_ID% >> "%TEMP%\vscode-fixed-id.bat"
echo start "" "%%LOCALAPPDATA%%\Programs\Microsoft VS Code\Code.exe" %%* >> "%TEMP%\vscode-fixed-id.bat"

echo.
echo ✅ 机器ID已固定为: %FIXED_ID%
echo.
echo 使用说明:
echo 1. 重启VSCode
echo 2. 或者使用 %TEMP%\vscode-fixed-id.bat 启动VSCode
echo.
echo 如需恢复原始设置:
echo 1. 删除环境变量 VSCODE_MACHINE_ID 和 VSCODE_SESSION_ID
echo 2. 删除文件 %VSCODE_DIR%\machineId
echo 3. 恢复备份文件 state.vscdb.backup

pause